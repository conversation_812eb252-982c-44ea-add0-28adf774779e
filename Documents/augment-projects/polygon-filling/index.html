<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Polygon Filling App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        body { font-family: "Inter", sans-serif; }
        .canvas-container {
            border: 2px dashed #d1d5db; border-radius: 0.5rem; background-color: #f9fafb;
            position: relative; width: 100%; max-width: 600px;
        }
        canvas {
            display: block; background-color: white; width: 100%; height: 100%; cursor: crosshair;
        }
        .instruction-overlay {
            position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            color: #9ca3af; text-align: center; pointer-events: none; transition: opacity 0.3s;
        }
        .loader-overlay {
            position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(255, 255, 255, 0.85);
            display: flex; justify-content: center; align-items: center; z-index: 10;
            flex-direction: column; gap: 1rem; border-radius: 0.5rem;
        }
        .draggable-obstacle {
            position: absolute; background-color: rgba(239, 68, 68, 0.5); border: 2px solid #b91c1c;
            cursor: grab; z-index: 20;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex flex-col items-center justify-center min-h-screen p-4">
    <div class="w-full max-w-5xl bg-white rounded-xl shadow-lg p-6 md:p-8">
        <header class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Polygon Filling App 🖼️</h1>
            <p class="text-gray-600 mt-2">Draw a polygon, add obstacles, and fill it with rectangles.</p>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
                <label for="rectWidthInput" class="font-medium text-sm">Rectangle Width:</label>
                <input type="number" id="rectWidthInput" value="20" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" disabled />
            </div>
            <div>
                <label for="rectHeightInput" class="font-medium text-sm">Rectangle Height:</label>
                <input type="number" id="rectHeightInput" value="50" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" disabled />
            </div>
            <div>
                <label for="rowGapInput" class="font-medium text-sm">Row Gap:</label>
                <input type="number" id="rowGapInput" value="5" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
                <label for="angleStepInput" class="font-medium text-sm">Angle Step:</label>
                <input type="number" id="angleStepInput" value="5" step="1" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
                <label for="obstacleWidthInput" class="font-medium text-sm">Obstacle Width:</label>
                <input type="number" id="obstacleWidthInput" value="30" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div>
                <label for="obstacleHeightInput" class="font-medium text-sm">Obstacle Height:</label>
                <input type="number" id="obstacleHeightInput" value="20" class="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <div class="flex items-center gap-2 col-span-1 lg:col-span-2">
                <button id="addObstacleButton" class="w-full bg-yellow-500 text-white font-semibold py-2 px-4 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200" disabled>
                    Add Obstacle
                </button>
            </div>
            <div class="flex items-center gap-2 col-span-full">
                <button id="finishDrawingButton" class="flex-1 bg-blue-600 text-white font-semibold py-2 px-5 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 hidden">
                    Finish Drawing
                </button>
                <button id="optimizeButton" class="flex-1 bg-green-600 text-white font-semibold py-2 px-5 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200" disabled>
                    Start Filling
                </button>
                <button id="resetButton" class="flex-1 bg-red-500 text-white font-semibold py-2 px-5 rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                    Reset
                </button>
            </div>
        </div>

        <div id="messageArea" class="text-center text-gray-600 font-medium h-6 mb-4"></div>

        <div class="flex justify-center">
            <div class="w-full">
                <h2 class="text-xl font-semibold text-center mb-2">Drawing and Filling Area</h2>
                <div id="canvasContainer" class="canvas-container mx-auto" style="aspect-ratio: 4/3">
                    <div id="drawingInstruction" class="instruction-overlay">
                        <p class="text-lg">Click the canvas to add vertices and draw a polygon</p>
                        <p class="text-sm">(Click the starting point to close the shape)</p>
                    </div>
                    <canvas id="mainCanvas"></canvas>
                    <div id="loader" class="loader-overlay hidden">
                        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p id="loaderText" class="text-blue-700 font-semibold">Processing...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const mainCanvas = document.getElementById("mainCanvas");
        const canvasContainer = document.getElementById("canvasContainer");
        const ctx = mainCanvas.getContext("2d");
        const optimizeButton = document.getElementById("optimizeButton");
        const resetButton = document.getElementById("resetButton");
        const finishDrawingButton = document.getElementById("finishDrawingButton");
        const addObstacleButton = document.getElementById("addObstacleButton");
        const rectWidthInput = document.getElementById("rectWidthInput");
        const rectHeightInput = document.getElementById("rectHeightInput");
        const angleStepInput = document.getElementById("angleStepInput");
        const rowGapInput = document.getElementById("rowGapInput");
        const obstacleWidthInput = document.getElementById("obstacleWidthInput");
        const obstacleHeightInput = document.getElementById("obstacleHeightInput");
        const messageArea = document.getElementById("messageArea");
        const drawingInstruction = document.getElementById("drawingInstruction");
        const loader = document.getElementById("loader");
        const loaderText = document.getElementById("loaderText");
        let vertices = [], obstacles = [], isProcessing = false, isPolygonClosed = false, draggingObstacleInfo = null;
        const CLOSE_ENOUGH_DISTANCE = 10;
        const FLOAT_TOLERANCE = 0.01;

        function resizeCanvas() {
            const container = mainCanvas.parentElement;
            const { width } = container.getBoundingClientRect();
            const height = (width * 3) / 4;
            mainCanvas.width = width;
            mainCanvas.height = height;
            drawOnCanvas();
        }

        function getMousePos(canvas, evt) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: (evt.clientX - rect.left) * (canvas.width / rect.width),
                y: (evt.clientY - rect.top) * (canvas.height / rect.height),
            };
        }

        function handleCanvasClick(event) {
            if (isProcessing || isPolygonClosed) return;
            const pos = getMousePos(mainCanvas, event);
            if (vertices.length >= 3) {
                const firstVertex = vertices[0];
                const dx = pos.x - firstVertex.x, dy = pos.y - firstVertex.y;
                if (Math.sqrt(dx * dx + dy * dy) < CLOSE_ENOUGH_DISTANCE) {
                    handleFinishDrawing();
                    return;
                }
            }
            drawingInstruction.style.opacity = "0";
            vertices.push(pos);
            drawOnCanvas();
            if (vertices.length >= 3) {
                finishDrawingButton.classList.remove("hidden");
                setMessage(`Defined ${vertices.length} vertices. Click the starting point or "Finish Drawing" to close the shape.`);
            } else {
                setMessage(`Defined ${vertices.length} vertices. At least ${3 - vertices.length} more needed.`);
            }
        }

        function drawOnCanvas() {
            ctx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
            if (isPolygonClosed && vertices.length > 2) {
                ctx.beginPath();
                ctx.moveTo(vertices[0].x, vertices[0].y);
                for (let i = 1; i < vertices.length; i++) ctx.lineTo(vertices[i].x, vertices[i].y);
                ctx.closePath();
                ctx.fillStyle = "rgba(59, 130, 246, 0.1)";
                ctx.fill();
            }
            ctx.fillStyle = "rgba(239, 68, 68, 0.7)";
            ctx.strokeStyle = "#b91c1c";
            ctx.lineWidth = 1;
            obstacles.forEach((obs) => {
                ctx.fillRect(obs.x, obs.y, obs.width, obs.height);
                ctx.strokeRect(obs.x, obs.y, obs.width, obs.height);
            });
            if (vertices.length > 0) {
                ctx.beginPath();
                ctx.moveTo(vertices[0].x, vertices[0].y);
                for (let i = 1; i < vertices.length; i++) ctx.lineTo(vertices[i].x, vertices[i].y);
                if (isPolygonClosed) ctx.closePath();
                ctx.strokeStyle = "#1f2937";
                ctx.lineWidth = 2;
                ctx.stroke();
            }
            vertices.forEach((v, index) => {
                ctx.beginPath();
                if (index === 0 && !isPolygonClosed && vertices.length > 0) {
                    ctx.fillStyle = "#f59e0b";
                    ctx.arc(v.x, v.y, 7, 0, 2 * Math.PI);
                } else {
                    ctx.fillStyle = "#3b82f6";
                    ctx.arc(v.x, v.y, 5, 0, 2 * Math.PI);
                }
                ctx.fill();
            });
        }

        function handleFinishDrawing() {
            if (vertices.length < 3) {
                setMessage("Polygon requires at least 3 vertices.", true);
                return;
            }
            isPolygonClosed = true;
            finishDrawingButton.classList.add("hidden");
            optimizeButton.disabled = false;
            addObstacleButton.disabled = false;
            mainCanvas.style.cursor = "default";
            drawOnCanvas();
            setMessage("Polygon completed! You can now add obstacles or start filling.", false);
        }

        function handleAddObstacleClick() {
            if (!isPolygonClosed || isProcessing || draggingObstacleInfo) return;
            const width = parseInt(obstacleWidthInput.value), height = parseInt(obstacleHeightInput.value);
            if (isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
                setMessage("Invalid obstacle dimensions.", true);
                return;
            }
            const obstacleEl = document.createElement("div");
            obstacleEl.className = "draggable-obstacle";
            obstacleEl.style.width = `${width}px`;
            obstacleEl.style.height = `${height}px`;
            obstacleEl.style.left = `${(canvasContainer.clientWidth - width) / 2}px`;
            obstacleEl.style.top = `${(canvasContainer.clientHeight - height) / 2}px`;
            canvasContainer.appendChild(obstacleEl);
            setMessage("Drag the obstacle to the desired position and release the mouse.");
            draggingObstacleInfo = { element: obstacleEl, width, height };
            obstacleEl.addEventListener("mousedown", handleDragStart);
        }

        function handleDragStart(e) {
            e.preventDefault();
            const el = draggingObstacleInfo.element;
            el.style.cursor = "grabbing";
            const rect = el.getBoundingClientRect();
            draggingObstacleInfo.offsetX = e.clientX - rect.left;
            draggingObstacleInfo.offsetY = e.clientY - rect.top;
            document.addEventListener("mousemove", handleDragMove);
            document.addEventListener("mouseup", handleDragEnd, { once: true });
        }

        function handleDragMove(e) {
            if (!draggingObstacleInfo) return;
            const contRect = canvasContainer.getBoundingClientRect();
            let x = e.clientX - contRect.left - draggingObstacleInfo.offsetX;
            let y = e.clientY - contRect.top - draggingObstacleInfo.offsetY;
            x = Math.max(0, Math.min(x, contRect.width - draggingObstacleInfo.width));
            y = Math.max(0, Math.min(y, contRect.height - draggingObstacleInfo.height));
            draggingObstacleInfo.element.style.left = `${x}px`;
            draggingObstacleInfo.element.style.top = `${y}px`;
        }

        function handleDragEnd(e) {
            const { element, width, height } = draggingObstacleInfo;
            const canvasRect = mainCanvas.getBoundingClientRect(), elementRect = element.getBoundingClientRect();
            const scaleX = mainCanvas.width / canvasRect.width, scaleY = mainCanvas.height / canvasRect.height;
            const newObstacle = {
                x: (elementRect.left - canvasRect.left) * scaleX,
                y: (elementRect.top - canvasRect.top) * scaleY,
                width: width,
                height: height,
            };
            const corners = [
                { x: newObstacle.x, y: newObstacle.y },
                { x: newObstacle.x + newObstacle.width, y: newObstacle.y },
                { x: newObstacle.x + newObstacle.width, y: newObstacle.y + newObstacle.height },
                { x: newObstacle.x, y: newObstacle.y + newObstacle.height },
            ];
            const isInside = corners.every((c) => isPointInPolygon(c, vertices));
            const doesOverlap = obstacles.some(
                (obs) =>
                    newObstacle.x < obs.x + obs.width &&
                    newObstacle.x + newObstacle.width > obs.x &&
                    newObstacle.y < obs.y + obs.height &&
                    newObstacle.y + newObstacle.height > obs.y
            );
            if (isInside && !doesOverlap) {
                obstacles.push(newObstacle);
                setMessage("Obstacle added successfully!", false);
            } else {
                setMessage(isInside ? "Obstacle overlaps with another obstacle." : "Obstacle must be fully inside the polygon.", true);
            }
            canvasContainer.removeChild(element);
            document.removeEventListener("mousemove", handleDragMove);
            draggingObstacleInfo = null;
            drawOnCanvas();
        }

        function resetAll() {
            if (isProcessing) return;
            vertices = [];
            obstacles = [];
            isProcessing = false;
            isPolygonClosed = false;
            optimizeButton.disabled = true;
            addObstacleButton.disabled = true;
            finishDrawingButton.classList.add("hidden");
            mainCanvas.style.cursor = "crosshair";
            if (draggingObstacleInfo) {
                canvasContainer.removeChild(draggingObstacleInfo.element);
                draggingObstacleInfo = null;
            }
            resizeCanvas();
            drawingInstruction.style.opacity = "1";
            setMessage("Canvas reset. Please draw a new polygon.");
        }

        function setMessage(msg, isError = false) {
            messageArea.textContent = msg;
            messageArea.style.color = isError ? "#dc2626" : "#16a34a";
        }

        function toggleLoading(show, text = "Processing...") {
            isProcessing = show;
            loader.classList.toggle("hidden", !show);
            loaderText.textContent = text;
            [optimizeButton, resetButton, finishDrawingButton, addObstacleButton].forEach((btn) => (btn.disabled = show));
        }

        function isPointInPolygon(point, vs) {
            const x = point.x, y = point.y;
            let inside = false;
            for (let i = 0, j = vs.length - 1; i < vs.length; j = i++) {
                const xi = vs[i].x, yi = vs[i].y;
                const xj = vs[j].x, yj = vs[j].y;
                if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
                    inside = !inside;
                }
            }
            return inside;
        }

        function getBoundingBox(points) {
            const xCoords = points.map((p) => p.x);
            const yCoords = points.map((p) => p.y);
            const minX = Math.min(...xCoords);
            const minY = Math.min(...yCoords);
            return { x: minX, y: minY, width: Math.max(...xCoords) - minX, height: Math.max(...yCoords) - minY };
        }

        function getPolygonCentroid(vs) {
            let signedArea = 0, cx = 0, cy = 0;
            for (let i = 0; i < vs.length; i++) {
                const x0 = vs[i].x, y0 = vs[i].y;
                const x1 = vs[(i + 1) % vs.length].x, y1 = vs[(i + 1) % vs.length].y;
                const a = x0 * y1 - x1 * y0;
                signedArea += a;
                cx += (x0 + x1) * a;
                cy += (y0 + y1) * a;
            }
            if (Math.abs(signedArea) < 1e-9) return { x: vs[0]?.x || 0, y: vs[0]?.y || 0 };
            signedArea *= 0.5;
            return { x: cx / (6 * signedArea), y: cy / (6 * signedArea) };
        }

        function rotatePoint(point, center, angleRad) {
            const cos = Math.cos(angleRad), sin = Math.sin(angleRad);
            const dx = point.x - center.x, dy = point.y - center.y;
            return { x: center.x + dx * cos - dy * sin, y: center.y + dx * sin + dy * cos };
        }

        function getPolygonSegmentsAtY(y, polygonVertices) {
            const intersections = [];
            for (let i = 0; i < polygonVertices.length; i++) {
                const p1 = polygonVertices[i];
                const p2 = polygonVertices[(i + 1) % polygonVertices.length];
                if ((p1.y < y && p2.y >= y) || (p2.y < y && p1.y >= y)) {
                    if (p2.y - p1.y !== 0) {
                        const x = ((y - p1.y) * (p2.x - p1.x)) / (p2.y - p1.y) + p1.x;
                        intersections.push(x);
                    }
                }
            }
            intersections.sort((a, b) => a - b);
            const segments = [];
            for (let i = 0; i < intersections.length; i += 2) {
                if (i + 1 < intersections.length) {
                    segments.push({ start: intersections[i], end: intersections[i + 1] });
                }
            }
            return segments;
        }

        function subtractIntervals(solidIntervals, holeIntervals) {
            let result = [...solidIntervals];
            holeIntervals.forEach((hole) => {
                let nextResult = [];
                result.forEach((solid) => {
                    if (solid.end <= hole.start || solid.start >= hole.end) {
                        nextResult.push(solid);
                    } else {
                        if (solid.start < hole.start) {
                            nextResult.push({ start: solid.start, end: hole.start });
                        }
                        if (solid.end > hole.end) {
                            nextResult.push({ start: hole.end, end: solid.end });
                        }
                    }
                });
                result = nextResult;
            });
            return result;
        }

        function getOffsetPolygon(polygon, offset) {
            const offsetVertices = [];
            const n = polygon.length;
            if (n < 3) return [];
            for (let i = 0; i < n; i++) {
                const p_prev = polygon[(i - 1 + n) % n], p_curr = polygon[i], p_next = polygon[(i + 1) % n];
                const v1 = { x: p_curr.x - p_prev.x, y: p_curr.y - p_prev.y },
                      v2 = { x: p_next.x - p_curr.x, y: p_next.y - p_curr.y };
                const n1 = { x: -v1.y, y: v1.x };
                const len1 = Math.sqrt(n1.x ** 2 + n1.y ** 2);
                if (len1 > FLOAT_TOLERANCE) { n1.x /= len1; n1.y /= len1; }
                const n2 = { x: -v2.y, y: v2.x };
                const len2 = Math.sqrt(n2.x ** 2 + n2.y ** 2);
                if (len2 > FLOAT_TOLERANCE) { n2.x /= len2; n2.y /= len2; }
                const miter_vec = { x: n1.x + n2.x, y: n1.y + n2.y };
                const miter_len = Math.sqrt(miter_vec.x ** 2 + miter_vec.y ** 2);
                if (miter_len > FLOAT_TOLERANCE) { miter_vec.x /= miter_len; miter_vec.y /= miter_len; }
                const cos_alpha = n1.x * miter_vec.x + n1.y * miter_vec.y;
                if (Math.abs(cos_alpha) < FLOAT_TOLERANCE) {
                    offsetVertices.push({ x: p_curr.x + n1.x * offset, y: p_curr.y + n1.y * offset });
                } else {
                    const miter_length = offset / cos_alpha;
                    offsetVertices.push({ x: p_curr.x + miter_vec.x * miter_length, y: p_curr.y + miter_vec.y * miter_length });
                }
            }
            return offsetVertices;
        }

        function doesRectangleOverlap(rect1, rect2) {
            const rect1AABB = {
                minX: Math.min(...rect1.map(pt => pt.x)),
                maxX: Math.max(...rect1.map(pt => pt.x)),
                minY: Math.min(...rect1.map(pt => pt.y)),
                maxY: Math.max(...rect1.map(pt => pt.y))
            };
            const rect2AABB = {
                minX: Math.min(...rect2.map(pt => pt.x)),
                maxX: Math.max(...rect2.map(pt => pt.x)),
                minY: Math.min(...rect2.map(pt => pt.y)),
                maxY: Math.max(...rect2.map(pt => pt.y))
            };
            return !(rect1AABB.maxX <= rect2AABB.minX || rect1AABB.minX >= rect2AABB.maxX ||
                     rect1AABB.maxY <= rect2AABB.minY || rect1AABB.minY >= rect2AABB.maxY);
        }

        function doesRectangleOverlapObstacle(rect, obstacle) {
            const rectAABB = {
                minX: Math.min(...rect.map(pt => pt.x)),
                maxX: Math.max(...rect.map(pt => pt.x)),
                minY: Math.min(...rect.map(pt => pt.y)),
                maxY: Math.max(...rect.map(pt => pt.y))
            };
            return !(rectAABB.maxX <= obstacle.x || rectAABB.minX >= obstacle.x + obstacle.width ||
                     rectAABB.maxY <= obstacle.y || rectAABB.minY >= obstacle.y + obstacle.height);
        }

        async function findOptimalFill(polygon, allObstacles) {
            let bestAngle = 0, maxRects = 0, bestRectsList = [];
            const angleStep = parseFloat(angleStepInput.value), rowGap = parseFloat(rowGapInput.value);
            const centroid = getPolygonCentroid(polygon);
            for (let angleDeg = 0; angleDeg < 90; angleDeg += angleStep) {
                const angleRad = (angleDeg * Math.PI) / 180;
                const rotatedVertices = polygon.map((v) => rotatePoint(v, centroid, -angleRad));
                const rotatedObstacles = allObstacles.map(obs => {
                    let corners;
                    if (obs.corners) {
                        corners = obs.corners.map(c => rotatePoint(c, centroid, -angleRad));
                    } else {
                        corners = [
                            { x: obs.x, y: obs.y },
                            { x: obs.x + obs.width, y: obs.y },
                            { x: obs.x + obs.width, y: obs.y + obs.height },
                            { x: obs.x, y: obs.y + obs.height }
                        ].map(c => rotatePoint(c, centroid, -angleRad));
                    }
                    const xs = corners.map(c => c.x), ys = corners.map(c => c.y);
                    return { minX: Math.min(...xs), maxX: Math.max(...xs), minY: Math.min(...ys), maxY: Math.max(...ys) };
                });
                const orientations = [ { w: 20, h: 50 }, { w: 50, h: 20 } ];
                if (20 === 50) orientations.pop();
                for (const orientation of orientations) {
                    const rectW = orientation.w, rectH = orientation.h;
                    let currentRectsList = [];
                    const polyBBox = getBoundingBox(rotatedVertices);
                    for (let y = polyBBox.y; y < polyBBox.y + polyBBox.height; y += rectH + rowGap) {
                        let availableSegments = getPolygonSegmentsAtY(y, rotatedVertices);
                        if (rectH > 0) {
                            const topSegments = getPolygonSegmentsAtY(y + rectH, rotatedVertices);
                            availableSegments = subtractIntervals(availableSegments, subtractIntervals(availableSegments, topSegments));
                        }
                        const fillableSegments = availableSegments;
                        fillableSegments.forEach((segment) => {
                            const segmentWidth = segment.end - segment.start;
                            const numRects = Math.floor(segmentWidth / rectW);
                            if (numRects === 0) return;
                            for (let i = 0; i < numRects; i++) {
                                const x = segment.start + i * rectW;
                                const rectCorners = [
                                    { x, y },
                                    { x: x + rectW, y },
                                    { x: x + rectW, y: y + rectH },
                                    { x, y: y + rectH }
                                ];
                                const allInside = rectCorners.every(pt => isPointInPolygon(pt, rotatedVertices));
                                if (!allInside) continue;
                                const rectAABB = {
                                    minX: Math.min(...rectCorners.map(pt => pt.x)),
                                    maxX: Math.max(...rectCorners.map(pt => pt.x)),
                                    minY: Math.min(...rectCorners.map(pt => pt.y)),
                                    maxY: Math.max(...rectCorners.map(pt => pt.y))
                                };
                                let overlap = false;
                                for (const obs of rotatedObstacles) {
                                    if (!(rectAABB.maxX <= obs.minX || rectAABB.minX >= obs.maxX || rectAABB.maxY <= obs.minY || rectAABB.minY >= obs.maxY)) {
                                        overlap = true;
                                        break;
                                    }
                                }
                                if (overlap) continue;
                                currentRectsList.push(rectCorners.map(c => rotatePoint(c, centroid, angleRad)));
                            }
                        });
                    }
                    if (currentRectsList.length > maxRects) {
                        maxRects = currentRectsList.length;
                        bestAngle = angleDeg;
                        bestRectsList = currentRectsList;
                    }
                }
            }
            return { bestRectsList, bestAngle, maxRects };
        }

        function getRemainingSegments(y, polygon, obstacles, placedRects) {
            let polySegments = getPolygonSegmentsAtY(y, polygon);
            const allObstacles = [
                ...obstacles.map(obs => ({
                    minX: obs.x,
                    maxX: obs.x + obs.width,
                    minY: obs.y,
                    maxY: obs.y + obs.height
                })),
                ...placedRects.map(rect => {
                    const xs = rect.corners.map(c => c.x);
                    const ys = rect.corners.map(c => c.y);
                    return {
                        minX: Math.min(...xs),
                        maxX: Math.max(...xs),
                        minY: Math.min(...ys),
                        maxY: Math.max(...ys)
                    };
                })
            ];
            let occupiedSegments = [];
            allObstacles.forEach(obs => {
                if (y >= obs.minY && y <= obs.maxY) {
                    occupiedSegments.push({ start: obs.minX, end: obs.maxX });
                }
            });
            return subtractIntervals(polySegments, occupiedSegments);
        }

        function fillRemainingAreas(polygon, obstacles, allPlacedRects) {
            const rectW = parseInt(rectWidthInput.value);
            const rectH = parseInt(rectHeightInput.value);
            const rowGap = parseFloat(rowGapInput.value);
            const polyBBox = getBoundingBox(polygon);
            let remainingRects = [];
            for (let y = polyBBox.y; y < polyBBox.y + polyBBox.height; y += rectH + rowGap) {
                const remainingSegments = getRemainingSegments(y, polygon, obstacles, allPlacedRects);
                remainingSegments.forEach(segment => {
                    const segmentWidth = segment.end - segment.start;
                    const numRects = Math.floor(segmentWidth / rectW);
                    for (let i = 0; i < numRects; i++) {
                        const x = segment.start + i * rectW;
                        const rectCorners = [
                            { x, y },
                            { x: x + rectW, y },
                            { x: x + rectW, y: y + rectH },
                            { x, y: y + rectH }
                        ];
                        const allInside = rectCorners.every(pt => isPointInPolygon(pt, polygon));
                        if (!allInside) continue;
                        const rectAABB = {
                            minX: Math.min(...rectCorners.map(pt => pt.x)),
                            maxX: Math.max(...rectCorners.map(pt => pt.x)),
                            minY: Math.min(...rectCorners.map(pt => pt.y)),
                            maxY: Math.max(...rectCorners.map(pt => pt.y))
                        };
                        const overlaps = allPlacedRects.some(rect => {
                            const obsXs = rect.corners.map(c => c.x);
                            const obsYs = rect.corners.map(c => c.y);
                            const obsAABB = {
                                minX: Math.min(...obsXs),
                                maxX: Math.max(...obsXs),
                                minY: Math.min(...obsYs),
                                maxY: Math.max(...obsYs)
                            };
                            return !(rectAABB.maxX <= obsAABB.minX || rectAABB.minX >= obsAABB.maxX || rectAABB.maxY <= obsAABB.minY || rectAABB.minY >= obsAABB.maxY);
                        }) || obstacles.some(obs =>
                            !(rectAABB.maxX <= obs.x || rectAABB.minX >= obs.x + obs.width || rectAABB.maxY <= obs.y || rectAABB.minY >= obs.y + obs.height)
                        );
                        if (!overlaps) {
                            remainingRects.push(rectCorners);
                        }
                    }
                });
            }
            return remainingRects;
        }

        async function fillPolygon() {
            if (!isPolygonClosed || vertices.length < 3) {
                setMessage("Please draw and complete a polygon with at least 3 vertices.", true);
                return;
            }
            toggleLoading(true, "Filling edges...");
            await new Promise((r) => setTimeout(r, 50));

            // Step 1: Edge filling with non-overlapping rectangles
            const area = (() => {
                let a = 0;
                for (let i = 0; i < vertices.length; i++) {
                    const p1 = vertices[i], p2 = vertices[(i + 1) % vertices.length];
                    a += (p1.x * p2.y) - (p2.x * p1.y);
                }
                return a;
            })();
            const workingPoly = area > 0 ? vertices : [...vertices].reverse();
            const edgeRectWidth = 20, edgeRectHeight = 50;
            let edgeRects = [];
            const edgeRectGroups = []; // Store rectangles by edge
            for (let i = 0; i < workingPoly.length; i++) {
                const p1 = workingPoly[i];
                const p2 = workingPoly[(i + 1) % workingPoly.length];
                const edgeVector = { x: p2.x - p1.x, y: p2.y - p1.y };
                const edgeLength = Math.hypot(edgeVector.x, edgeVector.y);
                if (edgeLength < 1e-6) continue;
                const edgeDir = { x: edgeVector.x / edgeLength, y: edgeVector.y / edgeLength };
                let currentEdgeRects = [];
                let currentDist = edgeRectWidth / 2;
                while (currentDist < edgeLength) {
                    const basePoint = { x: p1.x + edgeDir.x * currentDist, y: p1.y + edgeDir.y * currentDist };
                    const normalDir = { x: -edgeDir.y, y: edgeDir.x };
                    const w_half = edgeRectWidth / 2;
                    const base_p1 = { x: basePoint.x - edgeDir.x * w_half, y: basePoint.y - edgeDir.y * w_half };
                    const base_p2 = { x: basePoint.x + edgeDir.x * w_half, y: basePoint.y + edgeDir.y * w_half };
                    const top_p2 = { x: base_p2.x + normalDir.x * edgeRectHeight, y: base_p2.y + normalDir.y * edgeRectHeight };
                    const top_p1 = { x: base_p1.x + normalDir.x * edgeRectHeight, y: base_p1.y + normalDir.y * edgeRectHeight };
                    const rectCorners = [base_p1, base_p2, top_p2, top_p1];
                    const rectAABB = {
                        minX: Math.min(...rectCorners.map(pt => pt.x)),
                        maxX: Math.max(...rectCorners.map(pt => pt.x)),
                        minY: Math.min(...rectCorners.map(pt => pt.y)),
                        maxY: Math.max(...rectCorners.map(pt => pt.y))
                    };
                    const overlapsObstacle = obstacles.some(obs =>
                        !(rectAABB.maxX <= obs.x || rectAABB.minX >= obs.x + obs.width ||
                          rectAABB.maxY <= obs.y || rectAABB.minY >= obs.y + obs.height)
                    );
                    if (!overlapsObstacle) {
                        currentEdgeRects.push(rectCorners);
                    }
                    currentDist += edgeRectWidth;
                }
                edgeRectGroups.push({ edgeIndex: i, rects: currentEdgeRects });
            }

            // Resolve corner overlaps
            for (let i = 0; i < edgeRectGroups.length; i++) {
                const currentGroup = edgeRectGroups[i];
                const nextGroup = edgeRectGroups[(i + 1) % edgeRectGroups.length];
                const prevGroup = edgeRectGroups[(i - 1 + edgeRectGroups.length) % edgeRectGroups.length];
                const currentEdgeLength = Math.hypot(
                    workingPoly[(currentGroup.edgeIndex + 1) % workingPoly.length].x - workingPoly[currentGroup.edgeIndex].x,
                    workingPoly[(currentGroup.edgeIndex + 1) % workingPoly.length].y - workingPoly[currentGroup.edgeIndex].y
                );
                const nextEdgeLength = Math.hypot(
                    workingPoly[(nextGroup.edgeIndex + 1) % workingPoly.length].x - workingPoly[nextGroup.edgeIndex].x,
                    workingPoly[(nextGroup.edgeIndex + 1) % workingPoly.length].y - workingPoly[nextGroup.edgeIndex].y
                );
                const prevEdgeLength = Math.hypot(
                    workingPoly[(prevGroup.edgeIndex + 1) % workingPoly.length].x - workingPoly[prevGroup.edgeIndex].x,
                    workingPoly[(prevGroup.edgeIndex + 1) % workingPoly.length].y - workingPoly[prevGroup.edgeIndex].y
                );
                let filteredRects = [...currentGroup.rects];
                // Check first rectangle for overlap with previous edge's last rectangle
                if (filteredRects.length > 0 && prevGroup.rects.length > 0) {
                    const firstRect = filteredRects[0];
                    const lastPrevRect = prevGroup.rects[prevGroup.rects.length - 1];
                    if (doesRectangleOverlap(firstRect, lastPrevRect)) {
                        if (currentEdgeLength >= prevEdgeLength) {
                            prevGroup.rects.pop();
                        } else {
                            filteredRects.shift();
                        }
                    }
                }
                // Check last rectangle for overlap with next edge's first rectangle
                if (filteredRects.length > 0 && nextGroup.rects.length > 0) {
                    const lastRect = filteredRects[filteredRects.length - 1];
                    const firstNextRect = nextGroup.rects[0];
                    if (doesRectangleOverlap(lastRect, firstNextRect)) {
                        if (currentEdgeLength >= nextEdgeLength) {
                            nextGroup.rects.shift();
                        } else {
                            filteredRects.pop();
                        }
                    }
                }
                currentGroup.rects = filteredRects;
            }
            edgeRects = edgeRectGroups.flatMap(group => group.rects);

            // Step 2: Core polygon
            const totalOffset = edgeRectHeight;
            const corePolygon = getOffsetPolygon(workingPoly, totalOffset);
            let coreValid = corePolygon && corePolygon.length >= 3 && corePolygon.every(p => isPointInPolygon(p, workingPoly));

            // Draw initial state
            ctx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
            ctx.beginPath();
            ctx.moveTo(vertices[0].x, vertices[0].y);
            vertices.slice(1).forEach((v) => ctx.lineTo(v.x, v.y));
            ctx.closePath();
            ctx.fillStyle = "rgba(59, 130, 246, 0.1)";
            ctx.fill();
            ctx.strokeStyle = "#1f2937";
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fillStyle = "rgba(0, 0, 255, 0.5)";
            ctx.strokeStyle = "rgba(0, 0, 128, 0.8)";
            ctx.lineWidth = 1;
            edgeRects.forEach(corners => {
                ctx.beginPath();
                ctx.moveTo(corners[0].x, corners[0].y);
                for (let i = 1; i < corners.length; i++) ctx.lineTo(corners[i].x, corners[i].y);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
            });
            ctx.fillStyle = "rgba(239, 68, 68, 0.7)";
            ctx.strokeStyle = "#b91c1c";
            obstacles.forEach((obs) => {
                ctx.fillRect(obs.x, obs.y, obs.width, obs.height);
                ctx.strokeRect(obs.x, obs.y, obs.width, obs.height);
            });

            if (!coreValid) {
                setMessage("Edge filling completed. Core polygon invalid.", true);
                toggleLoading(false);
                return;
            }

            ctx.beginPath();
            ctx.moveTo(corePolygon[0].x, corePolygon[0].y);
            for (let i = 1; i < corePolygon.length; i++) ctx.lineTo(corePolygon[i].x, corePolygon[i].y);
            ctx.closePath();
            ctx.fillStyle = "rgba(255, 193, 7, 0.3)";
            ctx.strokeStyle = "#007bff";
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fill();

            // Step 3: Fill core
            toggleLoading(true, "Filling core area...");
            await new Promise((r) => setTimeout(r, 50));
            const coreFillResult = await findOptimalFill(corePolygon, obstacles);
            const coreRects = coreFillResult.bestRectsList;

            // Step 4: Second filling pass
            toggleLoading(true, "Filling remaining areas (pass 2)...");
            await new Promise((r) => setTimeout(r, 50));
            const allPlacedRectsPass1 = [...edgeRects, ...coreRects].map(corners => ({ corners }));
            const secondFillResult = await findOptimalFill(vertices, [...obstacles, ...allPlacedRectsPass1]);
            const additionalRectsPass2 = secondFillResult.bestRectsList;

            // Step 5: Fill remaining empty areas
            toggleLoading(true, "Filling final empty areas...");
            await new Promise((r) => setTimeout(r, 50));
            const allPlacedRects = [...allPlacedRectsPass1, ...additionalRectsPass2.map(corners => ({ corners }))];
            const remainingRects = await fillRemainingAreas(vertices, obstacles, allPlacedRects);

            // Final drawing
            ctx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
            ctx.beginPath();
            ctx.moveTo(vertices[0].x, vertices[0].y);
            vertices.slice(1).forEach((v) => ctx.lineTo(v.x, v.y));
            ctx.closePath();
            ctx.fillStyle = "rgba(59, 130, 246, 0.1)";
            ctx.fill();
            ctx.strokeStyle = "#1f2937";
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fillStyle = "rgba(0, 0, 255, 0.5)";
            ctx.strokeStyle = "rgba(0, 0, 128, 0.8)";
            ctx.lineWidth = 1;
            edgeRects.forEach(corners => {
                ctx.beginPath();
                ctx.moveTo(corners[0].x, corners[0].y);
                for (let i = 1; i < corners.length; i++) ctx.lineTo(corners[i].x, corners[i].y);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
            });
            const rectFillColor = "#a5b4fc", rectBorderColor = "#6366f1";
            coreRects.forEach((corners) => {
                ctx.beginPath();
                ctx.moveTo(corners[0].x, corners[0].y);
                corners.slice(1).forEach((c) => ctx.lineTo(c.x, c.y));
                ctx.closePath();
                ctx.fillStyle = rectFillColor;
                ctx.fill();
                ctx.strokeStyle = rectBorderColor;
                ctx.lineWidth = 0.5;
                ctx.stroke();
            });
            additionalRectsPass2.forEach((corners) => {
                ctx.beginPath();
                ctx.moveTo(corners[0].x, corners[0].y);
                corners.slice(1).forEach((c) => ctx.lineTo(c.x, c.y));
                ctx.closePath();
                ctx.fillStyle = rectFillColor;
                ctx.fill();
                ctx.strokeStyle = rectBorderColor;
                ctx.lineWidth = 0.5;
                ctx.stroke();
            });
            remainingRects.forEach((corners) => {
                ctx.beginPath();
                ctx.moveTo(corners[0].x, corners[0].y);
                corners.slice(1).forEach((c) => ctx.lineTo(c.x, c.y));
                ctx.closePath();
                ctx.fillStyle = "#93c5fd";
                ctx.fill();
                ctx.strokeStyle = "#3b82f6";
                ctx.lineWidth = 0.5;
                ctx.stroke();
            });
            ctx.fillStyle = "rgba(239, 68, 68, 0.7)";
            ctx.strokeStyle = "#b91c1c";
            obstacles.forEach((obs) => {
                ctx.fillRect(obs.x, obs.y, obs.width, obs.height);
                ctx.strokeRect(obs.x, obs.y, obs.width, obs.height);
            });
            setMessage(`Filling completed! Core: ${coreFillResult.maxRects} rects at ${coreFillResult.bestAngle.toFixed(1)}°. Pass 2: ${additionalRectsPass2.length} rects. Remaining: ${remainingRects.length} rects.`, false);
            toggleLoading(false);
        }

        mainCanvas.addEventListener("click", handleCanvasClick);
        finishDrawingButton.addEventListener("click", handleFinishDrawing);
        addObstacleButton.addEventListener("click", handleAddObstacleClick);
        optimizeButton.addEventListener("click", fillPolygon);
        resetButton.addEventListener("click", resetAll);
        window.addEventListener("resize", resizeCanvas);
        window.onload = () => {
            resizeCanvas();
            setMessage("Click on the canvas to define the polygon's vertices.");
        };
    </script>
</body>
</html>